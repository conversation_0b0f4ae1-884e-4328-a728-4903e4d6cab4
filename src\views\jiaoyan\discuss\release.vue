<template>
    <div class="bg">
        <div class="w1280">
            <div class="flex">
                <back>返回</back>
                <div class="title">我发布的</div>
            </div>
            <div class="boxItem" v-for="(item,index) in boxList" :key="index" @click="goDetail(item)">
                <div class="topBox">
                    <div class="userAndtime">
                        <div class="userName">{{ item.name }}</div>
                        <div class="time">{{ item.createTime }}</div>
                    </div>
                    <div class="rightBtn">
                        <div class="btn">
                            <img src="@/assets/dianzan_def.png" alt="" class="btnImg">
                            <div class="number">{{ item.likeNum || '0'}}</div>
                        </div>
                        <div class="btn">
                            <img src="@/assets/pinglun_def.png" alt="" class="btnImg">
                            <div class="number">{{ item.numberSum || '0'}}</div>
                        </div>
                        <el-popconfirm
                            title="是否确认删除？"
                            @confirm="deleteTopic(item)"
                        >
                            <template #reference>
                                <div class="btn" @click.stop>
                                    <img src="@/assets/delete.png" alt="" class="btnImg">
                                </div>
                            </template>
                        </el-popconfirm>
                    </div>
                </div>
                <div class="content">{{ item.title }}</div>
                <div class="content">{{ item.content }}</div>
                <div class="imgBox">
                    <div class="imgList" v-for="(imgItem,imgIndex) in item.fileInfoEntityList" :key="imgIndex">
                        <img :src="imgItem.url" alt="" class="imgSize">
                    </div>
                </div>
            </div>
        </div>
        <el-pagination
            v-model:current-page="pageParams.pageNum"
            v-model:page-size="pageParams.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :background="true"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            style="margin-top: 20px; justify-content: center;"
        />

    </div>
</template>
    
<script setup>
import { useRouter, useRoute } from 'vue-router'
import { getMessageBoardList, deleteMessageBoard } from '@/api/study.js'
const router = useRouter()
const route = useRoute()
const boxList = ref([]);
const pageParams = ref({
    pageNum: 1,
    pageSize: 10,
    requestType:1,
    researchOfficeId: route.query.id
})
const total = ref(0)
const loadData = () => {
    getMessageBoardList(pageParams.value).then(res => {
        boxList.value = res.data
        total.value = res.page.total
    })
}
const goDetail = (item) => {
    router.push({
        path: '/jiaoyanshi/discussDetail',
        query: {
            id: item.id
        }
    })
}
const deleteTopic = (item) => {
    deleteMessageBoard({
        id: item.id
    }).then(res => {
        if (res.status == 0) {
            ElMessage.success('删除成功')
            loadData()
            router.push({
                path: '/jiaoyanshi/discuss',
                query: {
                    id: route.query.id
                }
            })
        }
    })
}
const handleSizeChange = (val) => {
    pageParams.value.pageSize = val
    pageParams.value.pageNum = 1
    loadData()
}

const handleCurrentChange = (val) => {
    pageParams.value.pageNum = val
    loadData()
};

onMounted(() => {
    loadData()
});

</script>
      
<style lang="scss" scoped>
.bg {
  background: #f5f7fa;
  padding: 31px;
}
.box{
    display: flex;
    margin-top: 20px;
}
.leftBox{
    margin-right: 20px;
}
.rightBox{
    width: 236px;
    height: 300px;
    background: #FFFFFF;
    border-radius: 8px 8px 8px 8px;
    padding: 30px;
}
.boxItem{
    min-height: 149px;
    background: #FFFFFF;
    border-radius: 8px 8px 8px 8px;
    box-sizing: border-box;
    padding: 30px;
    margin-bottom: 20px;
}
.topBox{
    display: flex;
    justify-content: space-between;
}
.userAndtime{
    display: flex;
    align-items: center;
}
.userName{
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 500;
    font-size: 14px;
    color: #43474D;
    margin-right: 16px;
}
.time{
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 400;
    font-size: 13px;
    color: #878D99;
    margin-top: 4px;
}
.rightBtn{
    display: flex;
    align-items: center;
    justify-content: space-between;
}
.btn{
    display: flex;
    align-items: center;
    margin-right: 60px;
    cursor: pointer;
}
.btn:last-child{
    margin-right: 0px;
}
.btnImg{
    width: 24px;
    height: 24px;
    margin-right: 8px;
}
.number{
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 400;
    font-size: 14px;
    color: #878D99;
}
.content{
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 400;
    font-size: 14px;
    color: #44474D;
    line-height: 28px;
    margin-top: 12px;
}
.imgBox{
    display: grid;
    gap: 16px;
    grid-template-columns: repeat(6, 1fr);
    margin-top: 16px;
}
.imgSize{
    width: 180px;
    height: 135px;
    border-radius: 8px 8px 8px 8px;
}
.imgSize:last-child{
    margin-right: 0;
}
.flex{
    display: flex;
    align-items: center;
    margin-bottom: 16px;
}
.title{
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 500;
    font-size: 20px;
    color: #2D2F33;
    margin-left: 32px;
}
</style>
  