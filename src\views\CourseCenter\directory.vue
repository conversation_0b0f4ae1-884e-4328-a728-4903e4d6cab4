<template>
  <div class="directoryContent">
    <div class="directoryItem" v-for="(item, index) in videoList" :key="index" @click="goVideoDetail(item)">
      <img src="@/assets/playbtn.png" alt="" class="playBtnImg">
      <div class="title">{{ item.name }}</div>
      <div class="shikan" v-if="item.isFree == 1">可试看</div>
      <img v-if="item.isFree == 2" src="@/assets/lock.png" alt="" class="lockImg">
      <div class="time">{{ formatDuration(item.videoTotalTime) }}</div>
    </div>
    <div v-if="!videoList || videoList.length == 0" class="empty-message">
      暂无视频资源
    </div>
  </div>
</template>

<script setup>
import { ElMessage } from 'element-plus';
import { useRouter,useRoute } from 'vue-router';

const router = useRouter()
const route = useRoute()
const props = defineProps({
    videoList:{
        type:Array,
        default:()=>{
            return new Array()
        }
    }
})
const formatDuration = (seconds) =>{
    if (!seconds) return '0分钟0秒';
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}分钟${remainingSeconds}秒`;
}
const goVideoDetail = (item) =>{
    if (item.isFree == 1) {
        router.push({
            path: '/coursecenter/videoPlayer',
            query: {
                id: route.query.id,
                videoId: item.id
            }
        });
    } else {
        ElMessage({
            message: '请先购买课程即可解锁全部视频',
            type: 'warning'
        })
    }
}
</script>

<style lang="scss" scoped>
.directoryItem{
  height: 48px;
  background: #F5F6F7;
  border-radius: 8px 8px 8px 8px;
  display: flex;
  align-items: center;
  padding: 0 20px;
  margin-bottom: 24px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.directoryItem:hover {
  background-color: #EBEEF5;
}
.playBtnImg{
  width: 20px;
  height: 20px;
  margin-right: 12px;
}
.title{
  // margin-top: 10px;
}
.shikan{
  width: 68px;
  height: 24px;
  background: #386CFC;
  border-radius: 17px 17px 17px 17px;
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 400;
  font-size: 12px;
  color: #FFFFFF;
  font-style: normal;
  text-transform: none;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 12px;
  margin-right: 24px;
}
.time{
  height: 24px;
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 400;
  font-size: 16px;
  color: #878D99;
  line-height: 24px;
  text-align: right;
  font-style: normal;
  text-transform: none;
  margin-left: auto;
}
.lockImg{
  width: 16px;
  height: 16px;
  margin-left: 12px;
}
</style>