<template>
    <div class="container">
        <back>返回</back>
        <div class="video">
            <div class="videoDetail">
                <video-player
                class="video-player vjs-custom-skin"
                ref="videoPlayerRef"
                v-bind="playerOptions"
                :playsinline="true"
                >
                </video-player>
            </div>
            <div class="videoList">
                <div class="listTitle">课程视频</div>
                <div 
                    class="videoItem" 
                    v-for="(item, index) in detailInfo.videoVoList" 
                    :key="index"
                    @click="changeVideo(item)"
                    :class="{ active: videoId === item.id }"
                >
                    <img src="@/assets/playbtn.png" alt="" class="playIcon">
                    <div class="videoName">{{ item.name }}</div>
                    <div class="videoTime">{{ formatDuration(item.videoTotalTime) }}</div>
                    <div v-if="item.isFree == 1" class="freeTag">可试看</div>
                    <img v-else src="@/assets/lock.png" alt="" class="lockIcon">
                </div>
                <div v-if="!detailInfo.videoVoList || detailInfo.videoVoList.length == 0" class="emptyTip">
                    暂无视频资源
                </div>
            </div>
        </div>
    </div>
</template>
  
<script setup>
import back from '@/components/back.vue'
import videoPlayer  from 'vue3-video-play' 
import 'vue3-video-play/dist/style.css';
import { resourceDetail } from '@/api/index.js'
import { onBeforeUnmount, onMounted, reactive } from 'vue'
import { useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
const route = useRoute()
const videoId = ref('')
const detailInfo = ref({})
const videoPlayerRef = ref(null)
const playerOptions = reactive({
    width:'100%',
    height:'100%',
    autoplay: true,
    muted: false,
    loop: false,
    preload: 'auto',
    language: 'zh-CN',
    aspectRatio: '16:9',
    fluid: true,
    src:'',
    notSupportedMessage: '此视频暂无法播放，请稍后再试',
    controlBar: {
        timeDivider: true,
        durationDisplay: true,
        remainingTimeDisplay: false,
        fullscreenToggle: true
    }
})
function getDetailInfo() {
    if (!route.query.id) return;
    resourceDetail(route.query.id)
        .then(res => {
            if (res.data) {
                detailInfo.value = res.data;
                if (detailInfo.value.videoVoList && detailInfo.value.videoVoList.length > 0) {
                    const video = detailInfo.value.videoVoList.find(v => v.id === videoId.value);
                    if (video && video.videoUrl) {
                        playerOptions.src = video.videoUrl;
                        nextTick(() => {
                            if (videoPlayerRef.value) {
                                console.log('cccccccc=====>',videoPlayerRef.value);
                                videoPlayerRef.value.play();
                            }
                        });
                    }
                }
            }
        });
}
function changeVideo(item) {
    if (item.isFree == 1) {
        videoId.value = item.id;
        if (item.videoUrl) {
            playerOptions.src = item.videoUrl;
            // 更新播放器
            nextTick(() => {
                if (videoPlayerRef.value) {
                    console.log('cccccccc=====>',videoPlayerRef.value);
                    videoPlayerRef.value.play();
                }
            });
        }
    } else {
        ElMessage({
            message: '请先购买课程即可解锁全部视频',
            type: 'warning'
        });
    }
}
function formatDuration(seconds) {
    if (!seconds) return '0分钟0秒';
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}分钟${remainingSeconds}秒`;
}
onMounted(()=>{
    videoId.value = route.query.videoId;
    if (videoId.value) {
        getDetailInfo();
    }
})
onBeforeUnmount(()=>{
    if (videoPlayerRef.value) {
        videoPlayerRef.value.pause();
    }
})
</script>
  
<style lang="scss" scoped>
.container {
    padding: 20px;
    height: calc(100vh - 200px);
}
.video {
    display: flex;
    height: 100%;
    padding: 20px;
}

.videoDetail {
    flex: 3;
    background-color: #000;
    border-radius: 8px;
}

.videoList {
    flex: 1;
    margin-left: 20px;
    background-color: #f5f6f7;
    border-radius: 8px;
    padding: 15px;
    overflow-y: auto;
}

.listTitle {
    font-size: 18px;
    font-weight: 500;
    color: #2E2F33;
    margin-bottom: 15px;
}

.videoItem {
    display: flex;
    align-items: center;
    padding: 10px;
    background: #ffffff;
    border-radius: 6px;
    margin-bottom: 10px;
    cursor: pointer;
    transition: all 0.3s;
}

.videoItem:hover {
    background-color: #EBEEF5;
}

.videoItem.active {
    background-color: #E6F0FF;
    border-left: 3px solid #386CFC;
}

.playIcon {
    width: 16px;
    height: 16px;
    margin-right: 8px;
}

.videoName {
    flex: 1;
    font-size: 14px;
    color: #2E2F33;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.videoTime {
    font-size: 12px;
    color: #878D99;
    margin-right: 10px;
}

.freeTag {
    font-size: 12px;
    color: #fff;
    background-color: #386CFC;
    padding: 2px 6px;
    border-radius: 10px;
}

.lockIcon {
    width: 14px;
    height: 14px;
}

.emptyTip {
    text-align: center;
    color: #909399;
    padding: 20px 0;
}
</style>