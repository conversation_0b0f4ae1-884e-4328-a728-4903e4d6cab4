<template>
    <div class=" mdivcss">
      <div class="wid bgcss">
          <div class="filter-container">
            <FilterRow
              name="专业大类"
              :modelValue="selectedTheme"
              @update:modelValue="selectedTheme = $event"
              :list="themeList"
              :defaultCollapsed="true"
            />
          </div>
          <div class="mainContent">
            <div class="gridContainer" v-if="roomList.length">
              <div class="mainItem" v-for="(item, index) in roomList" :key="index" @click="goDetail(item)">
                <div class="mainItemTop">
                  <img :src="item.coverUrl"  class="topicImg">
                  <div class="videoTag" v-if="item.expertTagName">{{ item.expertTagName }}</div>
                </div>
                <div class="mainItemMiddle">
                  <div class="mainItemMiddleTitle">{{ item.name }}</div>
  
                    <div class="neirong">
                      <div class="lefttitlte">专业大类:</div>
                      <div class="righttitlte">{{ item.specialName }}</div>
                    </div>
                    <div class="neirong">
                      <div class="lefttitlte">层次:</div>
                      <div class="righttitlte">{{ levelMap[item.level] }}</div>
                    </div>
                    <div class="neirong">
                      <div class="lefttitlte">主办单位:</div>
                      <div class="righttitlte">{{ item.organizer }}</div>
                    </div>
                </div>
              </div>
            </div>
            <EmptyState v-else
              :emptyImage="nodataSize"
              alt="暂无数据"
            />
          </div>
      </div>
    </div>
  </template>
  
<script setup>
import nodataSize from '@/assets/nodataSize.png'
import FilterRow from '@/components/common/FilterRow.vue'
import EmptyState from '@/components/common/EmptyState.vue'
import { listAll,jiaoyanlist } from '@/api/api'
import { useRouter } from 'vue-router'

const router = useRouter()

const selectedTheme = ref('全部')
const themeList = ref([])
const roomList = ref([])
const levelMap = ref({
  1: '本科',
  2: '高职'
})
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  // userId: '',
  // schoolId: '',
  // type: 2,
  // classifyId: '',
  // isMyTeach: false
})

// 方法
const getMajorList = () => {
  const param = {
    tagtypeCode: 'TYPEMANAGER'
  }
  listAll(param)
    .then(res => {
      if (res.status == 0) {
        themeList.value = res.data.map(item => ({
          label: item.name,
          value: item.name,
          id: item.id
        }))
        themeList.value.unshift({
          label: '全部',
          value: '全部',
          id: ''
        })
      }
    })
}

const getOffLectureList = () => {
  jiaoyanlist(queryParams.value)
    .then(res => {
      if (res.status == 0) {
        roomList.value = res.data
      }
    })
}

const goDetail = (item) => {
  router.push({
    path: '/jiaoyanshi/detail',
    query: { id: item.id }
  })
}

// 监听器
watch(selectedTheme, (newVal) => {
  const selectedItem = themeList.value.find(item => item.value === newVal)
  queryParams.value.classifyId = selectedItem?.id || ''
  getOffLectureList()
})

// 生命周期
onMounted(() => {
  // getMajorList()
  getOffLectureList()
})
</script>
  
<style lang="scss" scoped>
.wid{
  width: 1280px;
}
.bgcss{
  margin: 0px auto;
}
.mdivcss{
  padding: 20px 0px;
  background-color: #F5F7FA;
  min-height: calc(100vh - 93px);
}
.filter-container {
  background-color: #fff;
  border-radius: 8px;
  padding: 15px 30px;
}
.mainContent{
  margin-top: 20px;
}
.topicImg{
  width: 100%;
  height: 172px;
  border-radius: 8px 8px 0px 0px;
}
.mainItem{
  background: #FFFFFF;
  border-radius: 8px 8px 8px 8px;
}
.mainItem:hover{
  cursor: pointer;
  box-shadow: 0px 2px 20px 0px rgba(56,108,252,0.16);
}
.gridContainer{
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
  width: 100%;
}
.mainItemMiddle{
  display: flex;
  justify-content: space-between;
  padding: 12px;
  padding-bottom: 16px;
  flex-direction: column;
}
.mainItemMiddleTop{
  display: flex;
  justify-content: space-between;
  margin-bottom: 12px;
}
.mainItemMiddleTitle{
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 500;
  font-size: 16px !important;
  color: #2D2F33;
  margin-bottom: 16px;
}
.mainItemNumber{
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 400;
  font-size: 13px;
  color: #878D99;
  .number {
    color: #FCA84E;
    font-weight: 500;
  }
}
.mainItemBottom{
  height: 56px;
  background: #F8F9FB;
  border-radius: 4px 4px 4px 4px;
}
.mainItemBottomTitle{
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 400;
  font-size: 13px;
  color: #878D99;
  line-height: 20px;
  margin: 8px;
}
.neirong{
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}
.neirong:last-child{
  margin-bottom: 0px;
}
.lefttitlte{
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 400;
  font-size: 14px;
  color: #666666;
  margin-right: 10px;
}
.righttitlte{
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 400;
  font-size: 14px;
  color: #666666;
}
.mainItemTop {
  position: relative;
}

.videoTag {
  position: absolute;
  top: 0;
  left: 0;
  width: 100px;
  height: 27px;
  line-height: 27px;
  text-align: center;
  background: #FF9B2F;
  border-radius: 8px 0px 12px 0px;
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 500;
  font-size: 13px;
  color: #FFFFFF;
}
</style>