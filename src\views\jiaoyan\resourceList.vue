<template>
  <div class="mainbg">
    <div class="w1280 pd">
      <back>返回</back>
      <div class="searchdiv">
        <filter-row
          name="热门分类"
          :list="types"
          :modelValue="pageBean.type"
          @update:modelValue="pageBean.type = $event"
        />
      </div>
      <div class="listdiv">
        <div class="resitem" v-for="item in 16" :key="item">
          <img
            src="https://lanhu-oss-2537-2.lanhuapp.com/FigmaDDSSlicePNGdb618d119a4bae0f1aa8ca1da55b07a6.png"
            alt=""
          />
          <div class="title">2025税务规划</div>
          <div class="botcss">
            <span><img src="@/assets/personicon.png" alt="" />赵湾湾</span>
            <span><img src="@/assets/timeicon.png" alt="" />2025-06-33</span>
          </div>
        </div>
      </div>
      <el-pagination
        v-model:current-page="pageBean.pageNum"
        v-model:page-size="pageBean.pageSize"
        :background="true"
        layout="total, prev, pager, next, jumper"
        :total="total"
        @current-change="handleCurrentChange"
        style="margin-top: 20px; justify-content: center"
      />
    </div>
  </div>
</template>
    
<script setup>
import back from '@/components/back.vue'
import FilterRow from '@/components/common/FilterRow.vue'
import { reactive } from 'vue'
const types = ref([
  {
    label: '全部',
    value: '',
  },
  {
    label: 'AI辅助演示推广',
    value: 'AI辅助演示推广',
  },
  {
    label: 'AI辅助数据分析',
    value: 'AI辅助数据分析',
  },
  {
    label: 'AI辅助图像绘制',
    value: 'AI辅助图像绘制',
  },
  {
    label: 'AI辅助音视频制作',
    value: 'AI辅助音视频制作',
  },
])
const pageBean = reactive({
  pageNum: 1,
  pageSize: 10,
  type: '',
})
const total = ref(0)
const loaddata = () => {}
const handleCurrentChange = (page) => {
  pageBean.pageNum = 1
  loaddata()
}
</script>
      
<style lang="scss" scoped>
.mainbg {
  width: 100%;
  min-height: calc(100vh - 58px);
  background: linear-gradient(90deg, #f5f8fc 0%, #f7f5fc 100%) !important;
}
.pd {
  padding: 20px 0;
}

.searchdiv {
  margin-top: 20px;
  background: #ffffff;
  border-radius: 8px;
  padding: 20px 24px;
}
.listdiv {
  display: flex;
  flex-wrap: wrap;
  .resitem {
    width: calc(20% - 16px);
    background: #ffffff;
    border-radius: 8px;
    margin-right: 20px;
    margin-top: 20px;
    img {
      width: 100%;
      aspect-ratio: 16/9;
    }
    .title {
      font-family: Source Han Sans CN, Source Han Sans CN;
      font-weight: 500;
      font-size: 16px;
      color: #2d2f33;
      text-align: left;
      font-style: normal;
      text-transform: none;
      padding: 12px 8px 8px;
    }
    .botcss {
      display: flex;
      justify-content: space-between;
      padding: 0px 8px 16px 8px;
      span {
        height: 20px;
        font-family: Source Han Sans CN, Source Han Sans CN;
        font-weight: 400;
        font-size: 13px;
        color: #878d99;
        text-align: right;
        font-style: normal;
        text-transform: none;
      }
      img {
        width: 16px;
        top: 3px;
        right: 4px;
        height: 16px;
      }
    }
  }
  .resitem:hover {
    box-shadow: 0px 2px 20px 0px rgba(56, 108, 252, 0.16);
  }
  .resitem:nth-child(5n) {
    margin-right: 0px !important;
  }
}
</style>
  