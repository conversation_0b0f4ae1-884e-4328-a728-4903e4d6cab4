<template>
  <div class="mianbg">
    <div class="titlediv">
      <div class="title">轮播图管理</div>
      <el-button class="defbtn" :icon="Plus" @click="showAdd">添加轮播图</el-button>
    </div>
    <el-table class="mytable" :data="tableData" style="margin-top: 20px;">
      <el-table-column prop="name" label="轮播图" align="center" >
          <template #default="scope">
              <img class="bannerimg" src="https://lanhu-oss-2537-2.lanhuapp.com/FigmaDDSSlicePNG34fae479830af88be0c3b3fae515b3c4.png" alt="">
          </template>
      </el-table-column>
      <el-table-column prop="createTime" label="创建时间" align="center"  />
      <el-table-column prop="edit" label="操作" align="center"  >
          <template #default="scope">
              <el-button type="text" class="bbtn ml20" @click="toEdit(scope.row)">编辑</el-button>
              <el-divider direction="vertical" />
              <el-button type="text" class="bbtn mr20" @click="toDelete(scope.row)">删除</el-button>
          </template>
      </el-table-column>
    </el-table>
    <el-pagination
      v-model:current-page="pageBean.pageNum"
      v-model:page-size="pageBean.pageSize"
      :background="true"
      layout="total, prev, pager, next, jumper"
      :total="total"
      @current-change="handleCurrentChange"
      style="margin-top: 20px; justify-content: center;"/>
    <addbanner ref="addtypeRef" @reload="reloadAction" />
    <msg-dialog ref="msgdialogRef"/>
  </div>
</template>

<script setup>
import {
  Plus,
  Search
} from '@element-plus/icons-vue'
import addbanner from '../common/addbanner.vue';
import { onMounted, reactive } from 'vue';
import { ElMessageBox } from 'element-plus';


const pageBean = reactive({
  pageNum:1,
  pageSize:6,
  name:'',
  organizer:'',
  isMyTeach:true,
  createBy:localStorage.getItem('id')
})
const total = ref(0)
const tableData = ref([{
    name:'1111',
    id:1,
}])
const addtypeRef = ref()

const loadData = () =>{
  
}
const handleCurrentChange = (page) =>{
  pageBean.pageNum = page;
  loadData()
}
const showAdd = () =>{
  addtypeRef.value.show()
}
const reloadAction = ()=>{
  pageBean.pageNum = 1;
  loadData()
}
const toEdit = (data) =>{
  addtypeRef.value.show(data.id)
}
const msgdialogRef = ref()
function toDelete(row) {
  msgdialogRef.value.show({
    type:'edit',
    title:'删除',
    msg:'是否删除该轮播图？',
    submitBtnText:"确认删除",
    submitAction:()=>{
      console.log('cccccc====',row);
    }
  })
}
onMounted(()=>{

})
</script>

<style lang="scss" scoped>
.bannerimg{
    width: 92px;
    height: 39px;
}
.titlediv{
  display: flex;
  justify-content: space-between;
  align-items: center;
  .title{
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 500;
    font-size: 20px;
    color: #2D2F33;
    text-align: left;
    font-style: normal;
    text-transform: none;
  }
}
.searchdiv{
  margin-top: 20px;
  ::v-deep(.el-form-item){
    margin-right: 12px;
  }
}
.definput{
  width: 280px;
  height: 36px;
  border-radius: 4px;
  :v-deep(.el-input__inner){
    border: 1px solid #E1E4EB;
  }
}
.listdiv{
  display: flex;
  flex-wrap: wrap;
  .jiaoyanitem{
    width: calc(33.33% - 14px);
    margin-right: 20px;
  }
  .jiaoyanitem:nth-child(3n){
    margin-right: 0px;
  }
}

.topBtn{
    display: flex;
    justify-content: flex-end;
    margin-bottom: 20px;
}

.pagination-container {
    display: flex;
    justify-content: center;
    margin-top: 20px;
}
.bbtn{
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 400;
    font-size: 14px;
    color: #386CFC;
    text-align: left;
    font-style: normal;
    text-transform: none;
    &:hover{
        color:#386CFC
    }
    &:focus{
        color:#386CFC
    }
}
.ml20{
    margin-right: 20px;
}
.mr20{
    margin-left: 20px;
}
</style>