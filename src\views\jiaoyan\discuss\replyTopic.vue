<template>
  <div>
    <el-dialog
      title="回复话题"
      v-model="dialogVisible"
      width="50%"
      @close="handleClose"
      class="mydialog"
      :close-on-click-modal="false"
    >
    <el-form :model="form" :rules="rules" ref="formRef">
      <el-form-item label="内容" prop="content" label-width="60px">
        <el-input
          type="textarea"
          :rows="4"
          placeholder="请输入内容"
          v-model="form.content"
          maxlength="2000"
          show-word-limit
        ></el-input>
      </el-form-item>
      <el-form-item label="图片" label-width="60px">
        <EUploadImgVideo
          v-model="imgList"
          :limit="5"
          :onlyImage="true"
          accept="image/png,image/jpg,image/gif,image/jpeg"
          :acceptData="imgageData"
        >
          <div>
            <p class="wen">温馨提示：</p>
            <p>（1）支持jpg/jpeg/png格式； </p>
            <p>（2）建议单张图片不超过10M，最多可上传5张；</p>
          </div>
        </EUploadImgVideo>
      </el-form-item>
    </el-form>
      <template #footer>
        <span class="dialog-footer">
          <div class="tip">请注意您的发言，礼貌留言哦~</div>
          <el-button class="btns" type="primary" @click="submitForm">立即发布</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { reactive, ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import EUploadImgVideo from '@/components/EUploadImgVideo.vue'
import { questionSave } from '@/api/study.js'
import { getFileTypeResult } from '@/utils/tools.js'

const props = defineProps({
  researchOfficeId: {
    type: String,
    required: true
  },
  parentId: {
    type: String,
    required: true
  },
})

const emit = defineEmits(['refreshList'])

const formRef = ref()
const dialogVisible = ref(false)
const imgList = ref([])

const imgageData = reactive({
  accept: '.png,.jpg,.gif,.jpeg',
  maxsize: 1024 * 1024 * 10,
  maxsize_text: '10M',
  imageTypeList: ['image/png', 'image/jpg', 'image/jpeg', 'image/gif']
})

const form = reactive({
  parentId: '',
  title: '',
  content: '',
  type: '2',
  isRecover: '1',
  researchOfficeId:props.researchOfficeId,
  fileInfoEntityList: [],
})

const rules = reactive({
  content: [
    { required: true, message: '请输入回复内容', trigger: 'blur' }
  ],
})

const initForm = () => {
  form.parentId = props.parentId
  form.content = ''
  form.fileInfoEntityList = []
  imgList.value = []
}

const handleClose = () => {
  formRef.value.resetFields()
  initForm()
  imgList.value = []
}

const submitForm = () => {
  formRef.value.validate((valid) => {
    if (valid) {
      form.fileInfoEntityList = imgList.value.map(item => ({
        url: item.url,
        fileName: item.name,
        fileSize: item.size,
        fileType: getFileTypeResult(item.url)
      }))
      questionSave(form)
        .then(() => {
          ElMessage.success('回复成功')
          dialogVisible.value = false
          emit('refreshList')
          initForm()
        })
    } else {
      return false
    }
  })
}

// 初始化表单
onMounted(() => {
  initForm()
})

// 暴露方法给父组件调用
defineExpose({
  dialogVisible
});
</script>

<style lang="scss" scoped>
    .dialog-footer{
        display: flex;
        justify-content: flex-end;
    }
    .tip{
        font-family: Source Han Sans CN, Source Han Sans CN;
        font-weight: 400;
        font-size: 14px;
        color: #8A8C89;
        margin-top: 10px;
        margin-right: 32px;
    }
</style>

