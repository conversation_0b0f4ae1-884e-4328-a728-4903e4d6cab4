@use './variables.scss';

.page-container {
  width: 100%;
  max-width: var(--container-width);
  margin: 0 auto;
  padding: 0 var(--container-padding);
  box-sizing: border-box;
}

.page-w1200 {
  width: 100%;
  max-width: var(--container-width1200);
  margin: 0 auto;
  padding: 0 var(--container-padding);
  box-sizing: border-box;
}

.el-dropdown-menu__item:not(.is-disabled):hover,
.el-dropdown-menu__item:not(.is-disabled):focus {
  background-color: #ecf5ff;
  color: #66b1ff;
}