<template>
  <el-button @click.stop.prevent="sendCode" text class="sendCode">
    {{ codeNum == 60 ? '发送验证码' : `${codeNum}s发送验证码` }}
  </el-button>
</template>
 
<script setup>
import { ref } from 'vue'
const codeNum = ref(60)
let clearId = null
const isClickSend = ref(false)
const sendCode = async () => {
  if (isClickSend.value) return
  isClickSend.value = true
  // // const res = await getCode(mobile.value, 'login')
  clearId = setInterval(() => {
    codeNum.value--
    if (codeNum.value <= 0) {
      clearInterval(clearId)
      codeNum.value = 60
      isClickSend.value = false
    }
  }, 1000)
}
</script>

<style lang='scss' >
.sendCode {
  padding: 0 !important;
  font-family: Source <PERSON> CN, Source Han Sans CN;
  font-weight: 400;
  font-size: 14px !important;
  color: #386cfc !important;
  &:hover {
    background: none !important;
  }
}
</style>
