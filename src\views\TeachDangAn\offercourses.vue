<template>
  <div class="bgcss">
    <div class="headcss">
      <div class="ticon">开设课程</div>
      <el-button class="addbtns" v-if="isShow" size="small" @click="showAdd"
        >添加开设课程</el-button
      >
    </div>
    <div class="listdiv">
      <el-card class="carditem" v-for="item in list" :key="item.id">
        <template #header>
          <span>{{ item.name }}</span>
          <el-dropdown class="right" @command="(e) => commandAction(e, item)">
            <span class="bclass">
              更多操作<i class="el-icon-arrow-down el-icon--right"></i>
            </span>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="1">查看详情</el-dropdown-item>
                <el-dropdown-item command="2" v-if="isShow"
                  >编辑</el-dropdown-item
                >
                <el-dropdown-item command="3" v-if="isShow"
                  >删除</el-dropdown-item
                >
              </el-dropdown-menu>
            </template>

          </el-dropdown>
        </template>
        <div class="flex">
          <img class="coverimg" :src="item.coverUrl" alt="" />
          <div class="rdiv">
            <div class="rname">
              开课对象：<span>{{ item.courseObject || '--' }}</span>
            </div>
            <div class="rname">
              开课院系：<span>{{ item.facultyName || '--' }}</span>
            </div>
            <div class="rname">
              开课学期：<span>{{ item.semesterName || '--' }}</span>
            </div>
            <div class="rname">
              开课语言：<span>{{ item.courseLanguage || '--' }}</span>
            </div>
          </div>
        </div>
      </el-card>
    </div>
    <div class="bottomcss">
      <el-pagination
        :current-page="pageBean.pageNum"
        :page-size="pageBean.pageSize"
        :total="total"
        @current-change="changePage"
        background
      ></el-pagination>
    </div>
    <el-dialog
      class="adddialog"
      v-model="dialogVisible"
      title="开设课程详情"
      @close="close"
    >
      <el-card shadow="never">
        <template #header>
          <span>{{ info.name || '--' }}</span>
        </template>
        <div class="flex">
          <img class="coverimg" :src="info.coverUrl" alt="" />
          <div class="rdiv">
            <div class="rname">
              开课对象：<span>{{ info.courseObject || '--' }}</span>
            </div>
            <div class="rname">
              开课院系：<span>{{ info.facultyName || '--' }}</span>
            </div>
            <div class="rname">
              开课学期：<span>{{ info.semesterName || '--' }}</span>
            </div>
            <div class="rname">
              开课语言：<span>{{ info.courseLanguage || '--' }}</span>
            </div>
          </div>
        </div>
        <div class="ticon">课程简介</div>
        <div class="tcontcss">
          {{ info.courseIntro || '--' }}
        </div>
        <div class="ticon">教学目标</div>
        <div class="tcontcss">
          {{ info.teachingGoals || '--' }}
        </div>
        <div class="ticon">教学方法</div>
        <div class="tcontcss">
          {{ info.teachingMethod || '--' }}
        </div>
        <div class="ticon">教学反思</div>
        <div class="tcontcss">
          {{ info.teachingReflection || '--' }}
        </div>
      </el-card>
    </el-dialog>
    <el-dialog
      class="adddialog"
      width="493px"
      top="50px"
      v-model="dialogFormVisible"
      :title="dTitle"
      @close="close2"
    >
      <el-form label-width="100px" :model="form" :rules="rules" ref="myform">
        <el-form-item label="课程名称：" prop="name">
          <el-input placeholder="请输入课程名称" v-model="form.name"></el-input>
        </el-form-item>
        <el-form-item label="课程封面：" prop="coverUrl">
          <el-upload
            class="upload-demo"
            :action="getUrl"
            :headers="headers"
            :data="fileData"
            :accept="'.jpeg,.jpg,png,.JPEG,.JPG,.PNG'"
            :on-success="handleSuccess"
            multiple
            :limit="1"
            :file-list="fileList"
          >
            <el-button class="defbtn" type="primary">点击上传</el-button>
            <!-- <div slot="tip" class="el-upload__tip">只能上传jpg/png文件，且不超过500kb</div> -->
          </el-upload>
        </el-form-item>
        <el-form-item label="开课对象：" prop="courseObject">
          <el-input
            placeholder="请输入开课对象"
            v-model="form.courseObject"
          ></el-input>
        </el-form-item>

        <el-form-item label="开课院系:" prop="courseFaculty">
          <el-select v-model="form.courseFaculty" placeholder="请选择开课院系">
            <el-option
              :label="item.name"
              :value="item.id"
              v-for="(item, index) in departments"
              :key="item.index"
            ></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="开课学期:" prop="courseSemester">
          <el-select v-model="form.courseSemester" placeholder="请选择开课学期">
            <el-option
              :label="item.name"
              :value="item.id"
              v-for="(item, index) in xueqi"
              :key="item.index"
            ></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="开课语言：" prop="courseLanguage">
          <el-input
            placeholder="请输入开课语言"
            v-model="form.courseLanguage"
          ></el-input>
        </el-form-item>
        <el-form-item label="课程简介：" prop="courseIntro">
          <el-input
            type="textarea"
            v-model="form.courseIntro"
            :rows="5"
            maxlength="1000"
            show-word-limit
            placeholder="请输入课程简介"
          ></el-input>
        </el-form-item>
        <el-form-item label="教学目标：" prop="teachingGoals">
          <el-input
            type="textarea"
            v-model="form.teachingGoals"
            :rows="5"
            maxlength="1000"
            show-word-limit
            placeholder="请输入教学目标"
          ></el-input>
        </el-form-item>
        <el-form-item label="教学方法：" prop="teachingMethod">
          <el-input
            type="textarea"
            v-model="form.teachingMethod"
            :rows="5"
            maxlength="1000"
            show-word-limit
            placeholder="请输入教学方法"
          ></el-input>
        </el-form-item>
        <el-form-item label="教学反思：" prop="teachingReflection">
          <el-input
            type="textarea"
            v-model="form.teachingReflection"
            :rows="5"
            maxlength="1000"
            show-word-limit
            placeholder="请输入教学反思"
          ></el-input>
        </el-form-item>
      </el-form>
      <template #footer >
        <div class="btns">
          <el-button class="linebtn" @click="close2">取 消</el-button>
          <el-button class="defbtn40" type="primary" @click="submitAction">保 存</el-button>
        </div>
        
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import {
  filebagcourseList,
  filebagcourseSave,
  filebagcourseInfo,
  filebagcourseDelete,
  filebagcourseUpdate,
} from '@/api/center/index'

import { queryListAll } from '@/api/center/earuser.js'
import { useRoute } from "vue-router";
import { ElMessage } from "element-plus";
import { onMounted, reactive } from 'vue';
const route = useRoute()
const getUrl = computed(()=>{
  return import.meta.env.VITE_GLOB_BASE_URL + '/aliyun/oss/uploadFiles'
})
const isShow = ref(false)
const xueqi = ref([])
const departments = ref([])
const dialogVisible = ref(false)
const dialogFormVisible = ref(false)
const dTitle = ref('添加开设课程')
const headers = ref({
  Authorization: localStorage.getItem('token') 
})
var fileList = ref([])
const fileData = ref({ serviceName: 'web' })
const pageBean = reactive({
  pageNum: 1,
  pageSize: 4,
})
const total = ref(0)
const form = reactive({
    id:'',
    name: '',
    teachingGoals: '',
    teachingMethod: '',
    teachingReflection: '',
    coverUrl: '',
    coverName: '',
    coverSize: '',
    courseObject: '',
    courseSemester: '',
    courseLanguage: '',
    courseFaculty: '',
    courseIntro: '',
    fileUrl: '',
    fileName: '',
    fileSize: '',
})
const rules = ref({
  name: [
    { required: true, message: '请输入开设课程名称', trigger: 'blur' },
  ],
  teachingGoals: [
    { required: true, message: '请输入教学目标', trigger: 'blur' },
  ],
  teachingMethod: [
    { required: true, message: '请输入教学方法', trigger: 'blur' },
  ],
  teachingReflection: [
    { required: true, message: '请输入教学反思', trigger: 'blur' },
  ],
  coverUrl: [
    { required: true, message: '请输入课程封面', trigger: 'change' },
  ],
  courseIntro: [
    { required: true, message: '请输入课程介绍', trigger: 'blur' },
  ],
  courseObject: [
    { required: true, message: '请输入开设对象', trigger: 'blur' },
  ],
  courseSemester: [
    { required: true, message: '请输入开设学期', trigger: 'blur' },
  ],
  courseLanguage: [
    { required: true, message: '请输入开设语言', trigger: 'blur' },
  ],
  courseFaculty: [
    { required: true, message: '请选择开设院系', trigger: 'change' },
  ],
})
const info = ref({})
const list = ref([])
function getType() {
  queryListAll({ code: 'faculty' }).then((res) => {
    if (res.status == 0) {
      departments.value = res.data
    }
  })
}
function getTypeXueQi() {
  queryListAll({ code: 'semester' }).then((res) => {
    if (res.status == 0) {
      xueqi.value = res.data
    }
  })
}
function loaddata() {
  filebagcourseList(pageBean)
    .then((result) => {
      list.value = result.data
      total.value = result.page.total
    })
    .catch((err) => {})
}
function changePage(page) {
  pageBean.pageNum = page
  loaddata()
}
function showAdd() {
  dTitle.value = '添加开设课程'
  dialogFormVisible.value = true
}
function commandAction(idx, data) {
  console.log('-----', idx, data)
  if (idx == 1) {
    return toDetail(data, 1)
  }
  if (idx == 2) {
    return toDetail(data, 2)
  }
  if (idx == 3) {
    return deleteAction(data)
  }
}
function deleteAction(data) {
  ElMessageBox.confirm('此操作将删除当前数据, 是否继续?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  })
  .then(() => {
    deleteItem(data.id)
  })
  .catch(() => {})
}
function deleteItem(id) {
  filebagcourseDelete({
    id: id,
  })
    .then((result) => {
      if (result.data) {
        ElMessage({
          type: 'success',
          message: '删除成功!',
        })
        loaddata()
      } else {
        ElMessage({
          type: 'error',
          message: result.msg,
        })
      }
    })
    .catch((err) => {})
}
function handleSuccess(res, file) {
  if (res.data.url.length > 0) {
    form.coverName = res.data.fileName
    form.coverSize = res.data.size
    form.coverUrl = res.data.url
  } else {
    ElMessage.error(res.msg)
  }
}
function toDetail(data, type) {
  filebagcourseInfo(data.id)
    .then((result) => {
      if (type == 1) {
        info.value = result.data
        dialogVisible.value = true
      } else {
        form = result.data
        delete form.createTime
        delete form.createBy
        fileList.value = [
          { url: form.coverUrl, name: form.coverName },
        ]
        dialogFormVisible.value = true
        dTitle.value = '编辑开设课程'
      }
    })
    .catch((err) => {})
}
function close() {
  dialogVisible.value = false
}
const myform = ref()
function close2() {
  Object.keys(form).forEach((item) => {
    form[item] = ''
  })
  fileList.value = []
  myform.value.clearValidate()
  dialogFormVisible.value = false
}
function update() {
  filebagcourseUpdate(form)
    .then((result) => {
      if (result.data) {
        ElMessage.success('保存成功')
        loaddata()
        close2()
      } else {
        ElMessage.error(result.msg)
      }
    })
    .catch((err) => {})
}
function add() {
  filebagcourseSave(form)
    .then((result) => {
      if (result.data) {
        ElMessage.success('保存成功')
        loaddata()
        close2()
      } else {
        ElMessage.error(result.msg)
      }
    })
    .catch((err) => {})
}
function submitAction() {
  myform.value.validate((valid) => {
    if (valid) {
      if (form.id) {
        update()
      } else {
        add()
      }
    } else {
      return false
    }
  })
}

onMounted(()=>{
    pageBean.createBy = route.query.id
    if (localStorage.getItem('id') == route.query.id) {
      isShow.value = true
    }
    loaddata()
    getType()
    getTypeXueQi()
})
</script>

<style lang="scss" scoped>
.btns{
  display: flex;
  justify-content: center;
  align-items: center;
}
.bclass {
  color: #386cfc;
}
.tcontcss {
  font-weight: 400;
  font-size: 12px;
  color: #636663;
  line-height: 20px;
  margin-top: 4px;
  margin-bottom: 20px;
}
.ticon {
  font-size: 14px;
  font-weight: 500;
  color: #386CFC;
  line-height: 40px;
  padding-left: 10px;
}
.ticon::before {
  content: '|';
  right: 10px;
  background-color: #386CFC;
}
.rname {
  width: 100%;
  margin-left: 8px;
  line-height: 20px;
  margin-top: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  color: #999999;
}
.rname span {
  color: #666666;
}
.rdiv {
  width: calc(100% - 150px);
}
.flex {
  display: flex;
  width: 100%;
}
.coverimg {
  width: 150px;
  aspect-ratio: 216/122 !important;
}
.carditem {
  width: calc(50% - 10px);
  margin-right: 16px;
  margin-bottom: 20px;
}
.carditem:nth-child(2n) {
  margin-right: 0px;
}
.bottomcss {
  margin-top: 20px;
}
.bgcss {
  padding: 20px 0px !important;
}
.listdiv {
  margin-top: 20px;
  display: flex;
  flex-wrap: wrap;
}
.headcss {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  text-align: right;
}
.addbtns {
  background-color: #386CFC;
  color: white;
  border: none !important;
}
.addbtns:hover {
  background-color: #386CFC;
  color: white;
  border: none !important;
}
.addbtns:focus {
  background-color: #386CFC;
  color: white;
  border: none !important;
}
::v-deep(.el-dialog__body) {
  height: 600px !important;
  overflow: auto;
  
}
.mydialog{
  .el-input{
    ::v-deep(.el-input__inner){
      padding:0px 10px !important;
    }
  }
  
}
</style>
