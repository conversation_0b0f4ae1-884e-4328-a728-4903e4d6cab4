<template>
  <div class="bgcss">
    <div class="headcss">
      <div class="ticon">教材</div>
      <el-button v-if="isShow" class="addbtns" size="small" @click="showAdd"
        >添加教材</el-button
      >
    </div>
    <div class="listdiv">
      <el-card class="carditem" v-for="item in list" :key="item.id">
        <div slot="header" class="clearfix">
          <span>{{ item.name }}</span>
          <el-dropdown class="right" @command="(e) => commandAction(e, item)">
            <span class="bclass">
              更多操作<i class="el-icon-arrow-down el-icon--right"></i>
            </span>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item command="1">查看详情</el-dropdown-item>
              <el-dropdown-item command="2" v-if="isShow"
                >编辑</el-dropdown-item
              >
              <el-dropdown-item command="3" v-if="isShow"
                >删除</el-dropdown-item
              >
            </el-dropdown-menu>
          </el-dropdown>
        </div>
        <div class="flex">
          <img class="coverimg" :src="item.fileUrl" alt="" />
          <div class="rdiv">
            <div class="rname">
              ISBN：<span>{{ item.isbn || '--' }}</span>
            </div>
            <div class="rname">
              出版社：<span>{{ item.pressName || '--' }}</span>
            </div>
            <div class="rname">
              出版时间：<span>{{ item.publicationTime || '--' }}</span>
            </div>
            <div class="rname">
              教材字数：<span>{{ item.wordsNumber || '--' }}</span>
            </div>
          </div>
        </div>
      </el-card>
    </div>
    <div class="bottomcss">
      <el-pagination
        :current-page="pageBean.pageNum"
        :page-size="pageBean.pageSize"
        :total="total"
        @current-change="changePage"
        background
      ></el-pagination>
    </div>
    <el-dialog
      class="adddialog"
      width="493px"
      :visible="dialogVisible"
      title="教材详情"
      @close="close"
    >
      <el-card shadow="never">
        <template #header>
          <span>{{ info.name || '--' }}</span>
        </template>
        <div class="flex">
          <img class="coverimg" :src="info.fileUrl" alt="" />
          <div class="rdiv">
            <div class="rname">
              ISBN：<span>{{ info.isbn || '--' }}</span>
            </div>
            <div class="rname">
              出版社：<span>{{ info.pressName || '--' }}</span>
            </div>
            <div class="rname">
              出版时间：<span>{{ info.publicationTime || '--' }}</span>
            </div>
            <div class="rname">
              教材字数：<span>{{ info.wordsNumber || '--' }}</span>
            </div>
          </div>
        </div>
        <div class="tcontcss">
          {{ info.intro || '--' }}
        </div>
      </el-card>
    </el-dialog>
    <el-dialog
      class="adddialog"
      width="493px"
      v-model="dialogFormVisible"
      :title="dTitle"
      @close="close2"
    >
      <el-form label-width="100px" :model="form" :rules="rules" ref="myform">
        <el-form-item label="教材名称：" prop="name">
          <el-input placeholder="请输入教材名称" v-model="form.name"></el-input>
        </el-form-item>
        <el-form-item label="教材封面：" prop="fileUrl">
          <el-upload
            class="upload-demo"
            :action="getUrl"
            :headers="headers"
            :data="fileData"
            :accept="'.jpeg,.jpg,png,.JPEG,.JPG,.PNG'"
            :on-success="handleSuccess"
            multiple
            :limit="1"
            :file-list="fileList"
          >
            <el-button class="defbtn" type="primary">点击上传</el-button>
            <!-- <div slot="tip" class="el-upload__tip">只能上传jpg/png文件，且不超过500kb</div> -->
          </el-upload>
        </el-form-item>
        <el-form-item label="ISBN：" prop="isbn">
          <el-input placeholder="请输入ISBN" v-model="form.isbn"></el-input>
        </el-form-item>
        <el-form-item label="出版社：" prop="pressName">
          <el-input
            placeholder="请输入出版社"
            v-model="form.pressName"
          ></el-input>
        </el-form-item>
        <el-form-item label="出版时间：" prop="publicationTime">
          <el-input
            placeholder="请输入出版时间"
            v-model="form.publicationTime"
          ></el-input>
        </el-form-item>
        <el-form-item label="教材字数：" prop="wordsNumber">
          <el-input
            type="number"
            placeholder="请输入教材字数"
            v-model="form.wordsNumber"
          ></el-input>
        </el-form-item>
        <el-form-item label="教材简介：" prop="intro">
          <el-input
            type="textarea"
            v-model="form.intro"
            :rows="5"
            maxlength="1000"
            show-word-limit
            placeholder="请输入教材简介"
          ></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="btns">
          <el-button class="linebtn" @click="close2">取 消</el-button>
          <el-button class="defbtn40" @click="submitAction">保 存</el-button>
        </div>
        
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import {
  teachingmaterialList,
  teachingmaterialSave,
  teachingmaterialInfo,
  teachingmaterialDelete,
  teachingmaterialUpdate,
} from '@/api/center/index'
import { ElMessage, ElMessageBox } from 'element-plus'
import { onMounted, reactive, ref } from 'vue'
import { useRoute,useRouter } from 'vue-router'
const route = useRoute()
const router = useRouter()
const getUrl = ref(import.meta.env.VITE_GLOB_BASE_URL + '/aliyun/oss/uploadFiles')
const isShow = ref(false)
const dialogVisible = ref(false)
const headers = ref({
  Authorization: localStorage.getItem('token') 
})
var fileList = ref([])
const fileData = ref({ serviceName: 'web' })
const pageBean = reactive({
  pageNum: 1,
  pageSize: 4,
})
const total = ref(0)
var dialogFormVisible = ref(false)
const dTitle = ref('添加获奖与荣誉')
const form = reactive({
  name: '',
  isbn: '',
  publicationTime: '',
  pressName: '',
  wordsNumber: '',
  intro: '',
  fileUrl: '',
  fileName: '',
  fileSize: '',
})
const rules = ref({
  name: [{ required: true, message: '请输入教材名称', trigger: 'blur' }],
  isbn: [{ required: true, message: '请输入ISBN', trigger: 'blur' }],
  publicationTime: [
    { required: true, message: '请输入出版时间', trigger: 'blur' },
  ],
  pressName: [
    { required: true, message: '请输入出版社', trigger: 'blur' },
  ],
  wordsNumber: [
    { required: true, message: '请输入教材字数', trigger: 'change' },
  ],
  intro: [{ required: true, message: '请输入教材简介', trigger: 'blur' }],
  fileUrl: [
    { required: true, message: '请输入教材简介', trigger: 'blur' },
  ],
})
const info = ref({})
const list = ref([])

function loaddata() {
  teachingmaterialList(pageBean)
    .then((result) => {
      list.value = result.data
      total.value = result.page.total
    })
    .catch((err) => {})
}
function changePage(page) {
  pageBean.pageNum = page
  loaddata()
}
function showAdd() {
  dTitle.value = '添加教材'
  dialogFormVisible.value = true
}
function deleteAction(data) {
  ElMessageBox.confirm('此操作将删除当前数据, 是否继续?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(() => {
      deleteItem(data.id)
    })
    .catch(() => {})
}
function deleteItem(id) {
  teachingmaterialDelete({
    id: id,
  })
    .then((result) => {
      if (result.data) {
        ElMessage({
          type: 'success',
          message: '删除成功!',
        })
        loaddata()
      } else {
        ElMessage({
          type: 'error',
          message: result.msg,
        })
      }
    })
    .catch((err) => {})
}
function toDetail(data, type) {
  teachingmaterialInfo(data.id)
    .then((result) => {
      if (type == 1) {
        info.value = result.data
        dialogVisible.value = true
      } else {
        form = result.data
        delete form.createTime
        delete form.createBy
        fileList.value = [
          { url: form.fileUrl, name: form.fileName },
        ]
        dialogFormVisible.value = true
        dTitle.value = '编辑教材'
      }
    })
    .catch((err) => {})
}
function commandAction(idx, data) {
  console.log('-----', idx, data)
  if (idx == 1) {
    return toDetail(data, 1)
  }
  if (idx == 2) {
    return toDetail(data, 2)
  }
  if (idx == 3) {
    return deleteAction(data)
  }
}
function handleSuccess(res, file) {
  if (res.data.url.length > 0) {
    form.fileName = res.data.fileName
    form.fileSize = res.data.size
    form.fileUrl = res.data.url
  } else {
    ElMessage.error(res.msg)
  }
}

function close() {
  dialogVisible.value = false
}
const myform = ref()

function close2() {
  Object.keys(form).forEach((item) => {
    form[item] = ''
  })
  fileList.value = []
  myform.value.clearValidate()
  dialogFormVisible.value = false
}
function update() {
  teachingmaterialUpdate(form)
    .then((result) => {
      if (result.data) {
        ElMessage.success('保存成功')
        loaddata()
        close2()
      } else {
        ElMessage.error(result.msg)
      }
    })
    .catch((err) => {})
}
function add() {
  teachingmaterialSave(form)
    .then((result) => {
      if (result.data) {
        ElMessage.success('保存成功')
        loaddata()
        close2()
      } else {
        ElMessage.error(result.msg)
      }
    })
    .catch((err) => {})
}
function submitAction() {
  myform.value.validate((valid) => {
    if (valid) {
      if (form.id) {
        update()
      } else {
        add()
      }
    } else {
      return false
    }
  })
}
onMounted(()=>{
  pageBean.createBy = route.query.id
  if (localStorage.getItem('id') == route.query.id) {
    isShow.value = true
  }
  loaddata()
})
</script>

 <style lang="scss" scoped>
 .btns{
  display: flex;
  justify-content: center;
  align-items: center;
 }
.bclass {
  color: #386cfc;
}
.mydialog{
  ::v-deep(.el-dialog__body){
    height: 70vh;
    overflow: auto;
  }
}
.tcontcss {
  font-weight: 400;
  font-size: 12px;
  color: #636663;
  line-height: 20px;
  margin-top: 12px;
}
.ticon {
  font-size: 14px;
  font-weight: 500;
  color: #386CFC;
  line-height: 40px;
  padding-left: 10px;
}
.ticon::before {
  content: '|';
  right: 10px;
  background-color: #386CFC;
}
.rname {
  width: 100%;
  margin-left: 8px;
  line-height: 20px;
  margin-top: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  color: #999999;
}
.rname span {
  color: #666666;
}
.rdiv {
  width: calc(100% - 80px);
}
.flex {
  display: flex;
  width: 100%;
}
.coverimg {
  width: 80px;
  aspect-ratio: 3/4 !important;
}
.carditem {
  width: calc(50% - 10px);
  margin-right: 16px;
  margin-bottom: 20px;
}
.carditem:nth-child(2n) {
  margin-right: 0px;
}
.bottomcss {
  margin-top: 20px;
}
.bgcss {
  padding: 20px 0px !important;
}
.listdiv {
  margin-top: 20px;
  display: flex;
  flex-wrap: wrap;
}
.headcss {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  text-align: right;
}
.addbtns {
  background-color: #386CFC;
  color: white;
  border: none !important;
}
.addbtns:hover {
  background-color: #386CFC;
  color: white;
  border: none !important;
}
.addbtns:focus {
  background-color: #386CFC;
  color: white;
  border: none !important;
}
</style>