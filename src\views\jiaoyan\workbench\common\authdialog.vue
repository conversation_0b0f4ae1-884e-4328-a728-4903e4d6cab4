<template>
    <el-dialog 
    width="493px" 
    :title="dialogTitle" 
    v-model="dialogVisible" 
    class="adddialog"
    @close="close" 
    :close-on-click-modal="false"
    :style="{
        '--el-dialog-padding-primary':'0px',
    }">
        <el-form 
        :rules="rules" 
        ref="addform" 
        :model="form" 
        label-position="top">
                <el-form-item label="用户权限" prop="name">
                    <el-select v-model="form.auth" placeholder="请选择用户权限" class="wid100">
                        <el-option
                        v-for="item in auths"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                        />
                    </el-select>
                </el-form-item>
            </el-form>
        <template #footer>
            <div class="dialog-footer">
                 <el-button class="linebtn" @click="close">取消</el-button>
                 <el-button class="defbtn40" @click="onSubmit">同意</el-button>
            </div>
        </template>
    </el-dialog>
</template>

<script setup>
import { useRoute } from "vue-router";
import { ElMessage } from "element-plus";
import { onMounted, reactive } from 'vue';
const route = useRoute()
const auths = ref([
    {
        label:'主理人',
        value:1
    },
    {
        label:'助理',
        value:2
    }
])
const dialogVisible = ref(false)
const dialogTitle = ref('授权')
const form = reactive({
    id: '',
    auth:''
})
const rules = ref({
    auth: [
        { required: true, message: '请选择用户权限', trigger: 'change' }
    ]
})
const emits = defineEmits(['reload'])
const addform = ref()
const show = (id) =>{
    dialogVisible.value = true;
    dialogTitle.value = '授权'
}
function loadInfo(){

}
function resetForm() {
    fileList.value = [];
    for (let key in form) {
        form[key] = '';
    }
}
function close() {
    addform.value.resetFields();
    resetForm()
    dialogVisible.value = false
}
function onSubmit() {
    addform.value.validate((valid) => {
        if (valid) {
            const request = form.id ? 
                updateMaterial(form) : 
                saveMaterial(form);
            
            request.then(res => {
                if (res.status == 0) {
                    ElMessage.success(this.form.id ? '更新成功' : '保存成功');
                    dialogVisible.value = false;
                } else {
                    ElMessage.error(res.msg);
                }
            })
        } else {
            return false;
        }
    });
}

onMounted(()=>{

})
defineExpose({
    show
})
</script>

<style lang="scss" scoped>
.upload-demo {
  .el-upload {
    width: 100%;
  }
}

.dialog-footer {
    display: flex;
    justify-content: center;
}
</style>
<style lang="scss">
</style>