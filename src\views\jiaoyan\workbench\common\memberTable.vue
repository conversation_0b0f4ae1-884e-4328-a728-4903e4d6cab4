<template>
    <el-form class="myform" inline>
         <el-form-item>
            <el-input 
            class="definput" 
            placeholder="请输入名称"
            clearable
            v-model="pageBean.name"></el-input>
        </el-form-item>
        <el-form-item>
            <el-input 
            class="definput" 
            placeholder="请输入学校/单位名称"
            clearable
            v-model="pageBean.unitName"></el-input>
        </el-form-item>
        <el-form-item>
            <el-button class="defbtn" :icon="Search" @click="searchAction">搜索</el-button>
        </el-form-item>
    </el-form>
    <el-table class="mytable" :data="tableData" style="margin-top: 20px;">
        <el-table-column prop="name" label="名称" align="center" min-width="80px" />
        <el-table-column prop="name" label="年龄" align="center" width="80px" />
        <el-table-column prop="name" label="性别" align="center" width="80px"/>
        <el-table-column prop="name" label="属性" align="center" width="80px"/>
        <el-table-column prop="name" label="年龄" align="center" width="80px"/>
        <el-table-column prop="name" label="高校/企业" align="center" min-width="183px" />
        <el-table-column prop="name" label="部门" align="center" min-width="183px" />
        <el-table-column prop="name" label="职称" align="center" width="80px"/>
        <el-table-column prop="createTime" label="申请时间" align="center" width="157px"  />
        <el-table-column fixed="right" prop="edit" label="操作" align="center" width="120px"  >
            <template v-if="type == 1" #default="scope">
                <el-button type="text" class="bbtn" @click="changeAction(scope.row,1)">同意</el-button>
                <el-divider direction="vertical" />
                <el-button type="text" class="bbtn" @click="changeAction(scope.row,2)">拒绝</el-button>
            </template>
            <template v-else-if="type == 2" #default="scope">
                <el-button type="text" class="bbtn" @click="changeAction(scope.row,3)">授权</el-button>
            </template>
        </el-table-column>
    </el-table>
    <el-pagination
    v-model:current-page="pageBean.pageNum"
    v-model:page-size="pageBean.pageSize"
    :background="true"
    layout="total, prev, pager, next, jumper"
    :total="total"
    @current-change="handleCurrentChange"
    style="margin-top: 20px; justify-content: center;"/>
    <msg-dialog ref="msgdialogRef"/>
    <authdialog ref="authdialogRef"/>
</template>

<script setup>
import {
  Plus,
  Search
} from '@element-plus/icons-vue'
import authdialog from './authdialog.vue';
import { onMounted, reactive } from "vue"
const msgdialogRef = ref()
const authdialogRef = ref()
const props = defineProps({
    type:{
        type:Number,
        default:1
    }
})
const tableData = ref([
    {
        name:'111'
    }
])
const pageBean = reactive({
    pageNum:1,
    pageSize:10,
    name:'',
    unitName:''
})
const changeAction = (data,type) =>{

    if (type == 1) {
        msgdialogRef.value.show({
            type:'edit',
            title:'提醒',
            msg:'是否允许该用户加入教研室?',
            submitBtnText:'同意',
            submitAction:()=>{
                console.log("sssdddd",data,type);
            }
        })
    }else if (type == 2){
        msgdialogRef.value.show({
            type:'edit',
            title:'提醒',
            msg:'是否拒绝该用户加入教研室??',
            submitBtnText:'拒绝',
            submitAction:()=>{
                console.log("sssdddd",data,type);
            }
        })
    }else if( type == 3){
        authdialogRef.value.show()
    }

}
const loadData = () =>{
    console.log('ssssss',props.type,pageBean)
}
const searchAction = () =>{
    pageBean.pageNum = 1
    loadData()
}
const handleCurrentChange = (page) =>{
    pageBean.pageNum = page
    loadData()
}
onMounted(()=>{
    loadData()
})
</script>

<style lang="scss" scoped>
.mytable{
    margin-top: 0px !important;
}
</style>