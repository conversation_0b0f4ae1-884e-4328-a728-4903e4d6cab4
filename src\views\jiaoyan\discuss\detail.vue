<template>
    <div class="bg">
        <div class="w1280">
            <back>返回</back>
            <!-- 讨论详情 -->
            <div class="boxItem">
                <div class="title">{{ detailData.title  }}</div>
                <div class="topBox">
                    <div class="userAndtime">
                        <div class="userName">{{ detailData.name  }}</div>
                        <div class="time">{{ detailData.createTime  }}</div>
                    </div>
                    <div class="rightBtn">
                        <div class="btn" @click="toggleLike">
                            <img :src="detailData.isCaseLiked ? '/src/assets/dianzan_sel.png' : '/src/assets/dianzan_def.png'" alt="" class="btnImg">
                            <div class="number">{{ detailData.caseLikedNumber || 0 }}</div>
                        </div>
                        <div class="btn" @click="replyTopic">
                            <img src="@/assets/pinglun_def.png" alt="" class="btnImg">
                            <div class="number">{{ detailData.numberSum || 0 }}</div>
                        </div>
                        <!-- <div class="btn" v-if="detailData.isCreateAdd" @click="deleteTopic"> -->
                        <el-popconfirm
                            title="是否确认删除？"
                            @confirm="deleteTopic(detailData.id)"
                        >
                            <template #reference>
                                <div class="btn">
                                    <img src="@/assets/delete.png" alt="" class="btnImg">
                                </div>
                            </template>
                        </el-popconfirm>
                    </div>
                </div>
                <div class="content">{{ detailData.content }}</div>
                <div class="imgBox">
                    <img v-for="(item, index) in detailData.fileInfoEntityList" :key="index" :src="item.url" alt="" class="imgSize">
                </div>
            </div>

            <!-- 排序 -->
            <div class="flex">
                <div class="leftPX">
                    <div class="xuText">排序:</div>
                    <div class="paixu">
                        <el-select
                            v-model="value"
                            clearable
                            placeholder="排序方式"
                            class="select"
                        >
                        <el-option
                            v-for="item in options"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                            />
                        </el-select>
                    </div>
                </div>
                <div class="total">共<span class="replyNumber">{{ total||'0' }}</span>条回复</div>
            </div>
            <!-- 回复列表 -->
            <div class="boxItem" v-for="reply in replyList" :key="reply.id">    
                <div class="topBox">
                    <div class="userAndtime">
                        <div class="userName">{{ reply.name }}</div>
                        <div class="time">{{ reply.createTime }}</div>
                    </div>
                    <div class="rightBtn">
                        <div class="btn" @click="toggleReplyLike(reply)">
                            <img :src="reply.isCaseLiked ? '/src/assets/dianzan_sel.png' : '/src/assets/dianzan_def.png'" alt="" class="btnImg">
                            <div class="number">{{ reply.caseLikedNumber || 0 }}</div>
                        </div>
                        <div class="btn" @click="openReplyDialog(reply.name, reply.id)">
                            <img src="@/assets/pinglun_def.png" alt="" class="btnImg">
                            <div class="number">{{ reply.numberSum || 0 }}</div>
                        </div>
                        <el-popconfirm
                            title="是否确认删除？"
                            @confirm="deleteReply(reply.id)"
                        >
                            <template #reference>
                                <div class="btn" v-if="reply.isCreateAdd">
                                    <img src="@/assets/delete.png" alt="" class="btnImg">
                                </div>
                            </template>
                        </el-popconfirm>
                    </div>
                </div>
                <div class="replyContent">{{ reply.content }}</div>
                <div class="imgBox" >
                    <img v-for="(file, index) in reply.fileInfoEntityList" :key="index" :src="file.url" alt="" class="imgSize">
                </div>
                <!-- 二级回复列表 -->
                <div class="replyBox" v-for="subReply in reply.courseTopicList" :key="subReply.id">
                    <div class="topBox">
                        <div class="userAndtime">
                            <div class="userName">{{ subReply.name }}</div>
                            <div class="time">{{ subReply.createTime }}</div>
                        </div>
                        <div class="rightBtn">
                            <!-- <div class="btn" v-if="subReply.isCreateAdd" @click="deleteReply(subReply.id)"> -->
                            <el-popconfirm
                                title="是否确认删除？"
                                @confirm="deleteReply(subReply.id)"
                            >
                                <template #reference>
                                    <div class="btn">
                                        <img src="@/assets/delete.png" alt="" class="btnImg">
                                    </div>
                                </template>
                            </el-popconfirm>
                        </div>
                    </div>
                    <div class="replyContent">{{ subReply.content }}</div>
                </div>
            </div>
            <el-pagination
                v-model:current-page="pageParams.pageNum"
                v-model:page-size="pageParams.pageSize"
                :page-sizes="[10, 20, 50, 100]"
                :background="true"
                layout="total, sizes, prev, pager, next, jumper"
                :total="total"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                style="margin-top: 20px; justify-content: center;"
            />
        </div>

        <!-- 回复话题弹窗 -->
        <ReplyTopic
            ref="replyTopicRef"
            :researchOfficeId="researchOfficeId"
            :parentId="parentId"
            @refreshList="refreshList"
        />

        <!-- 回复评论弹窗 -->
        <ReplyDialog
            v-model:visible="replyDialogVisible"
            :parentId="currentReplyId"
            :replyName="currentReplyName"
            @refresh="refreshList"
        />
    </div>

</template>
    
<script setup>
import {getMessageDetails,deleteMessageBoard} from '@/api/study.js'
import { useRoute, useRouter } from 'vue-router'
import ReplyTopic from './replyTopic.vue'
import ReplyDialog from './ReplyDialog.vue'

const route = useRoute()
const router = useRouter()
const replyTopicRef = ref()
const pageParams = ref({
    pageNum: 1,
    pageSize: 10,
    id: route.query.id,
    sort:'' // 排序方式，1：时间顺序，2：时间倒序，3：点赞数
})

const value = ref('')
const options = [
  {
    value: '1',
    label: '时间顺序',
  },
  {
    value: '2',
    label: '时间倒序',
  },
  {
    value: '3',
    label: '点赞数',
  },
]

const researchOfficeId = ref(route.query.researchOfficeId )
const parentId = ref(route.query.id)
const replyDialogVisible = ref(false)
const currentReplyId = ref('')
const currentReplyName = ref('')

const detailData = ref({})

const replyList = ref([])
const total = ref(0)

const replyTopic = () => {
  replyTopicRef.value.dialogVisible = true
}

const openReplyDialog = (replyName, replyId) => {
  currentReplyName.value = replyName
  currentReplyId.value = replyId
  replyDialogVisible.value = true
}

const refreshList = () => {
  loadData()
}

const loadData = () => {
      getMessageDetails(pageParams.value).then(res => {
            detailData.value = res.data
            replyList.value = res.data.courseTopicList
            total.value = res.data.pageInfo.total
      })
  }

// 点赞/取消点赞
const toggleLike = () => {
    console.log('点赞操作')
}

// 回复点赞/取消点赞
const toggleReplyLike = (reply) => {
    console.log('回复点赞操作', reply.id)

}
// 删除话题
const deleteTopic = (id) => {
    deleteMessageBoard({
        id: id
    }).then(res => {
        if (res.status == 0) {
            ElMessage.success('删除成功')
            loadData()
            router.push({
                path: '/jiaoyanshi/discuss',
                query: {
                    id: route.query.id
                }
            })
        }
    })
}

// 删除回复
const deleteReply = (replyId) => {
    deleteMessageBoard({
        id: replyId
    }).then(res => {
        if (res.status == 0) {
            ElMessage.success('删除成功')
            loadData()
        }
    })
}

const handleSizeChange = (val) => {
    pageParams.value.pageSize = val
    pageParams.value.pageNum = 1
    loadData()
}

const handleCurrentChange = (val) => {
    pageParams.value.pageNum = val
    loadData()
};

onMounted(() => {
    loadData()
});
</script>
      
<style lang="scss" scoped>
.bg {
  background: #f5f7fa;
  padding: 31px;
}
.title{
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 500;
    font-size: 24px;
    color: #2D2F33;
    margin-bottom: 20px;
}
.boxItem{
    min-height: 149px;
    background: #FFFFFF;
    border-radius: 8px 8px 8px 8px;
    box-sizing: border-box;
    padding: 30px;
    margin-bottom: 20px;
    margin-top: 20px;
}
.topBox{
    display: flex;
    justify-content: space-between;
}
.userAndtime{
    display: flex;
    align-items: center;
}
.userName{
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 500;
    font-size: 14px;
    color: #43474D;
    margin-right: 16px;
}
.time{
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 400;
    font-size: 13px;
    color: #878D99;
    margin-top: 4px;
}
.rightBtn{
    display: flex;
    align-items: center;
    justify-content: space-between;
}
.btn{
    display: flex;
    align-items: center;
    margin-right: 60px;
    cursor: pointer;
}
.btn:last-child{
    margin-right: 0px;
}
.btnImg{
    width: 24px;
    height: 24px;
    margin-right: 8px;
}
.number{
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 400;
    font-size: 14px;
    color: #878D99;
}
.content{
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 400;
    font-size: 16px;
    color: #44474D;
    line-height: 28px;
    margin-top: 12px;
}
.imgBox{
    display: grid;
    gap: 16px;
    grid-template-columns: repeat(5, 1fr);
    margin-top: 16px;
}
.imgSize{
    width: 180px;
    height: 135px;
    border-radius: 8px 8px 8px 8px;
}
.imgSize:last-child{
    margin-right: 0;
}
.flex{
    display: flex;
    align-items: center;
    justify-content: space-between;
}
.leftPX{
    display: flex;
    align-items: center;
}
.xuText{
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 400;
    font-size: 14px;
    color: #44474D;
    margin-right: 5px;
}
.paixu{
    display: flex;
    align-items: center;
}
.total{
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 400;
    font-size: 14px;
    color: #878D99;
}
.replyNumber{
    color:#2D2F33;
}
.replyContent{
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 400;
    font-size: 14px;
    color: #44474D;
    line-height: 28px;
}


.replyBox{
    min-height: 94px;
    background: #F5F7FA;
    border-radius: 8px 8px 8px 8px;
    margin-top: 16px;
    padding: 20px;
}

</style>
<style lang="scss">
.select {
    .el-select__wrapper{
        width: 180px;
        height: 40px;
        background: #FFFFFF;
        border-radius: 4px 4px 4px 4px;
    }
}
.el-select-dropdown__item {
    padding: 0 !important;
    padding-top: 0 !important;
}

.el-select-dropdown__item:hover {
    background-color: #386CFC !important;
    color: #FFFFFF !important;
}

</style>
  